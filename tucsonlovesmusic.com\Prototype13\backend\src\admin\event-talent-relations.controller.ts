import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, Not, IsNull } from 'typeorm';
import { AuthGuard } from '../auth/auth.guard';
import * as sql from 'mssql';
import * as dotenv from 'dotenv';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { Talent } from '../talent/talent.entity';
import { TalentSyncLog } from '../talent/entities/talent-sync-log.entity';
import { Event } from '../events/events.entity';
import { EventTalent } from '../events/event-talent.entity';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import * as fs from 'fs';
import * as path from 'path';

interface EventMissingTalent {
  id: string;
  name: string;
  date: string;
  venue_name: string;
  azure_talent_ids: string[];
  postgres_talent_count: number;
}

interface EventTalentStatusCounts {
  total: number;
  missingTalents: number;
  potentialFixes: number;
  mismatchedTalents: number;
}

class UpdateEventTalentsDto {
  eventId: string;
  talentIds: string[];
}

@Controller('admin/event-talent-relations')
@UseGuards(AuthGuard)
export class EventTalentRelationsController {

  /**
   * Extract social media links from a description text
   * Used to populate social media fields when importing talents from Azure
   */
  private extractSocialLinksFromDescription(description: string): {
    website?: string;
    facebook?: string;
    twitter?: string;
    instagram?: string;
    youtube?: string;
    spotify?: string;
  } {
    const links = {
      website: undefined,
      facebook: undefined,
      twitter: undefined,
      instagram: undefined,
      youtube: undefined,
      spotify: undefined
    };
    
    if (!description) return links;
    
    // Extract URLs
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const urls = description.match(urlRegex) || [];
    
    for (const url of urls) {
      const lowerUrl = url.toLowerCase();
      if (lowerUrl.includes('facebook.com')) links.facebook = url;
      else if (lowerUrl.includes('twitter.com') || lowerUrl.includes('x.com')) links.twitter = url;
      else if (lowerUrl.includes('instagram.com')) links.instagram = url;
      else if (lowerUrl.includes('youtube.com')) links.youtube = url;
      else if (lowerUrl.includes('spotify.com')) links.spotify = url;
      else if (!links.website) links.website = url;
    }
    
    return links;
  }
  private azureConfig: sql.config;
  private azurePoolPromise: Promise<sql.ConnectionPool> | null = null;
  private connectionRetryAttempts = 3;
  private connectionRetryDelay = 3000; // 3 seconds
  private lastConnectionFailure: Date | null = null;
  private circuitBreakerWindow = 60000; // 1 minute circuit breaker
  
  constructor(@InjectDataSource() private dataSource: DataSource) {
    // Load environment variables
    dotenv.config();
    
    // Configure Azure SQL connection with best practices
    this.azureConfig = {
      server: process.env.AZURE_DB_SERVER || 'mssql.drv1.umbhost.net',
      database: process.env.AZURE_DB_NAME || 'TLM',
      user: process.env.AZURE_DB_USER || 'Reader',
      password: process.env.AZURE_DB_PASSWORD || 'TLM1234!',
      options: {
        encrypt: false,
        trustServerCertificate: true,
        enableArithAbort: true,
        connectTimeout: 30000, // 30 second timeout for connections
        requestTimeout: 30000  // 30 second timeout for requests
      },
      pool: {
        max: 10,           // Maximum number of connections
        min: 1,            // Minimum of 1 connection maintained
        idleTimeoutMillis: 30000,  // Close idle connections after 30 seconds
        acquireTimeoutMillis: 15000 // 15 seconds timeout when acquiring connection
      }
    };
  }
  
  /**
   * Get a connection to Azure SQL
   * This method uses connection pooling for better performance and implements
   * retry logic to handle transient connection issues
   */
  private async getAzurePool(): Promise<sql.ConnectionPool> {
    // Use robust connection with retry logic instead of simple connection
    console.log('🔗 Creating Azure SQL connection with retry logic...');
    
    try {
      // If we don't have an existing pool or it's closed, create a new one
      if (!this.azurePoolPromise) {
        return await this.createNewPool();
      }
      
      // Test the existing pool
      const pool = await this.azurePoolPromise;
      if (pool && pool.connected) {
        // Test the connection is actually working
        await pool.request().query('SELECT 1 AS test');
        console.log('✅ Reusing existing Azure SQL connection');
        return pool;
      } else {
        // Pool exists but not connected, create new one
        console.log('🔄 Existing pool not connected, creating new one...');
        await this.closeExistingPool();
        return await this.createNewPool();
      }
    } catch (error) {
      console.warn('🔄 Connection test failed, creating new pool:', 
        error instanceof Error ? error.message : 'Unknown error');
      await this.closeExistingPool();
      return await this.createNewPool();
    }
  }
  
  /**
   * Safely close the existing pool and reset the promise
   */
  private async closeExistingPool(): Promise<void> {
    if (this.azurePoolPromise) {
      try {
        const pool = await this.azurePoolPromise;
        if (pool && pool.connected) {
          await pool.close();
        }
      } catch (error) {
        console.warn('Error closing Azure SQL pool:', 
          error instanceof Error ? error.message : 'Unknown error');
      } finally {
        this.azurePoolPromise = null;
      }
    }
  }
  
  /**
   * Create a new connection pool with retry logic and circuit breaker
   */
  private async createNewPool(): Promise<sql.ConnectionPool> {
    // Circuit breaker: if we had a failure recently, don't try again too soon
    if (this.lastConnectionFailure) {
      const timeSinceFailure = Date.now() - this.lastConnectionFailure.getTime();
      if (timeSinceFailure < this.circuitBreakerWindow) {
        throw new Error(`Circuit breaker open: Azure connection failed recently, waiting ${Math.ceil((this.circuitBreakerWindow - timeSinceFailure) / 1000)}s before retry`);
      }
    }
    
    let retryCount = 0;
    let lastError: any = null;
    
    while (retryCount < this.connectionRetryAttempts) {
      try {
        // Create and connect to a new pool
        this.azurePoolPromise = new sql.ConnectionPool(this.azureConfig).connect();
        const pool = await this.azurePoolPromise;
        
        // Test the connection
        await pool.request().query('SELECT 1 AS testConnection');
        
        console.log(`✅ Successfully connected to Azure SQL (attempt ${retryCount + 1})`);
        // Reset circuit breaker on success
        this.lastConnectionFailure = null;
        return pool;
      } catch (error) {
        lastError = error;
        retryCount++;
        
        console.warn(`Azure SQL connection attempt ${retryCount} failed:`, 
          error instanceof Error ? error.message : 'Unknown error');
        
        // Reset the promise for the next attempt
        this.azurePoolPromise = null;
        
        // Wait before retrying, but only if we're going to retry again
        if (retryCount < this.connectionRetryAttempts) {
          await new Promise(resolve => setTimeout(resolve, this.connectionRetryDelay));
        }
      }
    }
    
    // If we've exhausted all retries, record failure time and throw the last error
    console.error('❌ All Azure SQL connection attempts failed, activating circuit breaker');
    this.lastConnectionFailure = new Date();
    throw lastError;
  }

  /**
   * Get status counts for event-talent relationships (base route)
   * Connects to both PostgreSQL and Azure SQL to compare relationships
   */
  @Get()
  async getEventTalentStatusCounts(@Query('year') year?: string): Promise<EventTalentStatusCounts> {
    return this.getEventTalentStatusCountsInternal(year);
  }

  /**
   * Get status counts for event-talent relationships (status route)
   * Connects to both PostgreSQL and Azure SQL to compare relationships
   */
  @Get('status')
  async getEventTalentStatusCountsStatus(@Query('year') year?: string): Promise<EventTalentStatusCounts> {
    return this.getEventTalentStatusCountsInternal(year);
  }

  /**
   * Internal method to get status counts for event-talent relationships
   * Connects to both PostgreSQL and Azure SQL to compare relationships
   */
  private async getEventTalentStatusCountsInternal(year?: string): Promise<EventTalentStatusCounts> {
    let azurePool = null;
    
    try {
      // Connect to Azure SQL with improved error handling
      azurePool = await this.getAzurePool();
      // Build the year filter condition
      const yearFilter = year ? `AND EXTRACT(YEAR FROM "startDateTime"::timestamp) >= ${parseInt(year)}` : '';
      
      // Get total events count in PostgreSQL
      const totalEventsResult = await this.dataSource.query(`
        SELECT COUNT(*) as total 
        FROM event 
        WHERE deleted = false ${yearFilter}
      `);
      const totalEvents = parseInt(totalEventsResult[0].total);
      
      // Get events in PostgreSQL with Azure IDs but no talent relationships
      const missingTalentsResult = await this.dataSource.query(`
        SELECT COUNT(DISTINCT e.id) as count 
        FROM event e
        LEFT JOIN event_talents et ON e.id = et.event_id
        WHERE e.deleted = false
        AND e.azure_id IS NOT NULL
        AND et.talent_id IS NULL
        ${yearFilter}
      `);
      const missingTalents = parseInt(missingTalentsResult[0].count);
      
      // Get events that have some talent relationships but may be incomplete
      const mismatchedResult = await this.dataSource.query(`
        WITH event_talent_counts AS (
          SELECT 
            e.id,
            e.azure_id,
            COUNT(et.talent_id) as talent_count
          FROM event e
          LEFT JOIN event_talents et ON e.id = et.event_id
          WHERE e.deleted = false AND e.azure_id IS NOT NULL ${yearFilter}
          GROUP BY e.id, e.azure_id
        )
        SELECT COUNT(*) as count
        FROM event_talent_counts etc
        WHERE etc.talent_count > 0
      `);
      const mismatchedTalents = parseInt(mismatchedResult[0].count);
      
      // Get events that can potentially be fixed (have Azure IDs)
      const potentialFixesResult = await this.dataSource.query(`
        SELECT COUNT(*) as count 
        FROM event e
        WHERE e.deleted = false
        AND e.azure_id IS NOT NULL
        ${yearFilter}
      `);
      const potentialFixes = parseInt(potentialFixesResult[0].count);
      
      return {
        total: totalEvents,
        missingTalents,
        mismatchedTalents,
        potentialFixes
      };
    } catch (error) {
      console.error('Error in getEventTalentStatusCountsInternal:', error instanceof Error ? error.message : 'Unknown error');
      
      // Handle various Azure connection issues
      if (error instanceof Error) {
        const errorMessage = error.message.toLowerCase();
        if (errorMessage.includes('connection is closed') || 
            errorMessage.includes('etimeout') || 
            errorMessage.includes('econnclosed') ||
            errorMessage.includes('failed to connect')) {
          console.warn('🔄 Azure connection issue detected in getEventTalentStatusCountsInternal, resetting pool for retry...');
          await this.closeExistingPool();
        }
      }
      
      throw error;
    }
  }

  /**
   * Get events with missing talent relationships
   * Checks PostgreSQL events with no talent relationships, then validates against MSSQL source of truth
   * to determine if the missing relationships are intentional or actually missing
   */
  @Get('missing')
  async getEventsWithMissingTalents(@Query('year') year?: string): Promise<EventMissingTalent[]> {
    let azurePool = null;
    const results = [];

    try {
      console.log(`🔍 Starting missing talents validation with year: ${year}`);

      // Connect to Azure SQL for validation
      azurePool = await this.getAzurePool();

      // Build the year filter condition
      const yearFilter = year ? `AND EXTRACT(YEAR FROM e."startDateTime"::timestamp) >= ${parseInt(year)}` : '';

      // Step 1: Get events from PostgreSQL that have 0 talent relationships but have Azure IDs
      const query = `
        SELECT
          e.id,
          e.name,
          e.azure_id,
          e."startDateTime" as date,
          v.name as venue_name,
          COUNT(et.talent_id) as postgres_talent_count
        FROM event e
        LEFT JOIN venue v ON e.venue_id = v.id
        LEFT JOIN event_talents et ON e.id = et.event_id
        WHERE e.deleted = false
        AND e.azure_id IS NOT NULL
        ${yearFilter}
        GROUP BY e.id, e.name, e."startDateTime", v.name, e.azure_id
        HAVING COUNT(et.talent_id) = 0
        ORDER BY e."startDateTime" ASC
        LIMIT 1000
      `;

      const eventsWithNoTalents = await this.dataSource.query(query);
      console.log(`🔍 Found ${eventsWithNoTalents.length} PostgreSQL events with 0 talent relationships`);

      if (eventsWithNoTalents.length === 0) {
        console.log(`✅ No events found with missing talent relationships`);
        return [];
      }

      // Step 2: Batch query MSSQL to check which of these events SHOULD have talents
      // Get all Azure IDs for batch validation
      const azureEventIds = eventsWithNoTalents.map(event => event.azure_id);

      // MSSQL has a 2100 parameter limit, so process in chunks
      const batchSize = 1000; // Safe batch size for MSSQL parameters
      const allAzureResults = [];

      for (let i = 0; i < azureEventIds.length; i += batchSize) {
        const batchEventIds = azureEventIds.slice(i, i + batchSize);

        // Build batch query for this chunk
        const eventIdParams = batchEventIds.map((_, index) => `@eventId${index}`).join(',');
        const azureRequest = azurePool.request();
        batchEventIds.forEach((azureId, index) => {
          azureRequest.input(`eventId${index}`, sql.UniqueIdentifier, azureId);
        });

        // Query MSSQL to find which events actually have talent relationships
        const batchResult = await azureRequest.query(`
          SELECT
            pemtm.PerformanceEventsId as EventId,
            COUNT(pemtm.TalentListId) as TalentCount,
            STRING_AGG(CAST(pemtm.TalentListId AS VARCHAR(36)), ',') as TalentIds
          FROM PerformanceEventModelTalentModel pemtm
          WHERE pemtm.PerformanceEventsId IN (${eventIdParams})
          GROUP BY pemtm.PerformanceEventsId
        `);

        allAzureResults.push(...batchResult.recordset);
        console.log(`🔍 MSSQL batch ${Math.floor(i / batchSize) + 1}: ${batchResult.recordset.length} events with talents`);
      }

      console.log(`🔍 MSSQL validation complete: ${allAzureResults.length} total events have talent relationships in source`);

      // Step 3: Create lookup map for events that SHOULD have talents according to MSSQL
      const mssqlEventTalentsMap = new Map();
      allAzureResults.forEach(row => {
        const eventId = row.EventId.toLowerCase();
        const talentIds = row.TalentIds ? row.TalentIds.split(',') : [];
        mssqlEventTalentsMap.set(eventId, {
          talentCount: row.TalentCount,
          talentIds: talentIds
        });
      });

      // Step 4: Compare PostgreSQL events against MSSQL source of truth
      for (const pgEvent of eventsWithNoTalents) {
        const azureId = pgEvent.azure_id.toLowerCase();
        const mssqlTalentData = mssqlEventTalentsMap.get(azureId);

        // Only include events that SHOULD have talents according to MSSQL but don't in PostgreSQL
        if (mssqlTalentData && mssqlTalentData.talentCount > 0) {
          results.push({
            id: pgEvent.id,
            name: pgEvent.name,
            date: pgEvent.date,
            venue_name: pgEvent.venue_name,
            azure_talent_ids: mssqlTalentData.talentIds,
            postgres_talent_count: 0
          });
        }
      }

      console.log(`✅ Validation complete: ${results.length} events are missing talent relationships that exist in MSSQL source`);
      console.log(`ℹ️  ${eventsWithNoTalents.length - results.length} events correctly have no talent relationships (matching MSSQL source)`);

      return results;

    } catch (error) {
      console.error('🔍 [ERROR] Error in getEventsWithMissingTalents:', error);

      // Handle various Azure connection issues
      if (error instanceof Error) {
        const errorMessage = error.message.toLowerCase();
        if (errorMessage.includes('connection is closed') ||
            errorMessage.includes('etimeout') ||
            errorMessage.includes('econnclosed') ||
            errorMessage.includes('failed to connect')) {
          console.warn('🔄 Azure connection issue detected in getEventsWithMissingTalents, resetting pool for retry...');
          await this.closeExistingPool();
        }
      }

      throw error;
    } finally {
      // Clean up Azure connection
      if (azurePool) {
        try {
          await azurePool.close();
        } catch (closeError) {
          console.warn('Warning: Failed to close Azure pool:', closeError);
        }
      }
    }
  }

  /**
   * Get events with mismatched talent relationships
   * Checks PostgreSQL events with talent relationships, then validates against MSSQL source of truth
   * to find events where the talent counts don't match
   */
  @Get('mismatched')
  async getEventsWithMismatchedTalents(@Query('year') year?: string): Promise<EventMissingTalent[]> {
    let azurePool = null;
    const results = [];

    try {
      console.log(`🔍 Starting mismatched talents validation with year: ${year}`);

      // Connect to Azure SQL for validation
      azurePool = await this.getAzurePool();

      // Build the year filter condition
      const yearFilter = year ? `AND EXTRACT(YEAR FROM e."startDateTime"::timestamp) >= ${parseInt(year)}` : '';

      // Step 1: Get events from PostgreSQL that have at least one talent relationship
      const query = `
        SELECT
          e.id,
          e.name,
          e.azure_id,
          e."startDateTime" as date,
          v.name as venue_name,
          COUNT(et.talent_id) as postgres_talent_count
        FROM event e
        LEFT JOIN venue v ON e.venue_id = v.id
        LEFT JOIN event_talents et ON e.id = et.event_id
        WHERE e.deleted = false
        AND e.azure_id IS NOT NULL
        ${yearFilter}
        GROUP BY e.id, e.name, e."startDateTime", v.name, e.azure_id
        HAVING COUNT(et.talent_id) > 0
        ORDER BY e."startDateTime" DESC
        LIMIT 500
      `;

      const eventsWithTalents = await this.dataSource.query(query);
      console.log(`🔍 Found ${eventsWithTalents.length} PostgreSQL events with talent relationships`);

      if (eventsWithTalents.length === 0) {
        console.log(`✅ No events found with talent relationships to validate`);
        return [];
      }

      // Step 2: Batch query MSSQL to get talent counts for these events
      const azureEventIds = eventsWithTalents.map(event => event.azure_id);

      // MSSQL has a 2100 parameter limit, so process in chunks
      const batchSize = 1000; // Safe batch size for MSSQL parameters
      const allAzureResults = [];

      for (let i = 0; i < azureEventIds.length; i += batchSize) {
        const batchEventIds = azureEventIds.slice(i, i + batchSize);

        // Build batch query for this chunk
        const eventIdParams = batchEventIds.map((_, index) => `@eventId${index}`).join(',');
        const azureRequest = azurePool.request();
        batchEventIds.forEach((azureId, index) => {
          azureRequest.input(`eventId${index}`, sql.UniqueIdentifier, azureId);
        });

        // Query MSSQL to get talent counts and IDs for this batch
        const batchResult = await azureRequest.query(`
          SELECT
            pemtm.PerformanceEventsId as EventId,
            COUNT(pemtm.TalentListId) as TalentCount,
            STRING_AGG(CAST(pemtm.TalentListId AS VARCHAR(36)), ',') as TalentIds
          FROM PerformanceEventModelTalentModel pemtm
          WHERE pemtm.PerformanceEventsId IN (${eventIdParams})
          GROUP BY pemtm.PerformanceEventsId
        `);

        allAzureResults.push(...batchResult.recordset);
        console.log(`🔍 MSSQL batch ${Math.floor(i / batchSize) + 1}: ${batchResult.recordset.length} events with talents`);
      }

      console.log(`🔍 MSSQL validation complete: ${allAzureResults.length} total events have talent relationships in source`);

      // Step 3: Create lookup map for MSSQL talent data
      const mssqlEventTalentsMap = new Map();
      allAzureResults.forEach(row => {
        const eventId = row.EventId.toLowerCase();
        const talentIds = row.TalentIds ? row.TalentIds.split(',') : [];
        mssqlEventTalentsMap.set(eventId, {
          talentCount: row.TalentCount,
          talentIds: talentIds
        });
      });

      // Step 4: Compare PostgreSQL talent counts against MSSQL source of truth
      for (const pgEvent of eventsWithTalents) {
        const azureId = pgEvent.azure_id.toLowerCase();
        const mssqlTalentData = mssqlEventTalentsMap.get(azureId);
        const postgresTalentCount = parseInt(pgEvent.postgres_talent_count);

        // Compare counts - include if they don't match
        const mssqlTalentCount = mssqlTalentData ? mssqlTalentData.talentCount : 0;

        if (mssqlTalentCount !== postgresTalentCount) {
          results.push({
            id: pgEvent.id,
            name: pgEvent.name,
            date: pgEvent.date,
            venue_name: pgEvent.venue_name,
            azure_talent_ids: mssqlTalentData ? mssqlTalentData.talentIds : [],
            postgres_talent_count: postgresTalentCount
          });
        }
      }

      console.log(`✅ Validation complete: ${results.length} events have mismatched talent counts between PostgreSQL and MSSQL`);

      return results;
    } catch (error) {
      console.error('Error in getEventsWithMismatchedTalents:', error instanceof Error ? error.message : 'Unknown error');
      
      // Handle various Azure connection issues
      if (error instanceof Error) {
        const errorMessage = error.message.toLowerCase();
        if (errorMessage.includes('connection is closed') || 
            errorMessage.includes('etimeout') || 
            errorMessage.includes('econnclosed') ||
            errorMessage.includes('failed to connect')) {
          console.warn('🔄 Azure connection issue detected in getEventsWithMismatchedTalents, resetting pool for retry...');
          await this.closeExistingPool();
        }
      }
      
      // Return partial results instead of failing completely
      console.log(`⚠️ Returning ${results.length} events that were successfully processed before error`);
      return results;
    }
  }

  @Post('fix')
  async fixEventTalentRelationship(@Body() updateDto: UpdateEventTalentsDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const { eventId, talentIds } = updateDto;

      // Clear existing talent relationships
      await queryRunner.query(`
        DELETE FROM event_talents 
        WHERE event_id = $1
      `, [eventId]);

      // Add new talent relationships
      for (const talentId of talentIds) {
        await queryRunner.query(`
          INSERT INTO event_talents (event_id, talent_id)
          VALUES ($1, $2)
        `, [eventId, talentId]);
      }

      await queryRunner.commitTransaction();
      return { success: true, message: 'Event-talent relationships updated successfully' };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Fix a single event's talent relationships by synchronizing from Azure
   */
  @Post('fix/:id')
  async fixSingleEventTalentRelationship(@Param('id') eventId: string) {
    console.log(`🔧 [Backend] Starting fix for event ID: ${eventId}`);
    let azurePool = null;
    
    // Use a query runner for PostgreSQL transactions
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    console.log(`🔧 [Backend] PostgreSQL transaction started`);
    
    try {
      // Connect to Azure SQL with improved error handling
      azurePool = await this.getAzurePool();

      // Create stats object to track progress
      const stats = {
        relationshipsAdded: 0,
        relationshipsRemoved: 0,
        talentsNotFound: 0
      };
      
      // Get the specific event from PostgreSQL
      const pgEvent = await queryRunner.query(`
        SELECT id, azure_id, name
        FROM event
        WHERE id = $1 AND deleted = false
      `, [eventId]);
      
      if (!pgEvent || pgEvent.length === 0 || !pgEvent[0].azure_id) {
        throw new Error(`Event not found or missing Azure ID: ${eventId}`);
      }

      const event = pgEvent[0];
      
      // Get all talents from PostgreSQL with their Azure IDs
      const pgTalents = await queryRunner.query(`
        SELECT id, azure_id, name
        FROM talent
        WHERE deleted = false AND azure_id IS NOT NULL
      `);
      
      // Create a map of Azure ID to PostgreSQL ID for quick lookups
      const talentAzureToPostgresMap = new Map();
      pgTalents.forEach(talent => {
        if (talent.azure_id) {
          talentAzureToPostgresMap.set(talent.azure_id.toLowerCase(), talent.id);
        }
      });
      
      // Get all existing event-talent relationships for this event
      const currentRelationships = await queryRunner.query(`
        SELECT talent_id FROM event_talents WHERE event_id = $1
      `, [event.id]);
      
      // Create a set of existing talent IDs for this event
      const currentEventTalentIds = new Set(currentRelationships.map(rel => rel.talent_id));

      // Get all talents for this event from Azure
      const azureResult = await azurePool.request()
        .input('eventId', sql.UniqueIdentifier, event.azure_id)
        .query(`
          SELECT t.Id as talentId
          FROM PerformanceEventModelTalentModel pemtm
          JOIN Talent t ON pemtm.TalentListId = t.Id
          WHERE pemtm.PerformanceEventsId = @eventId
        `);
      
      // Get Azure talent IDs and map them to PostgreSQL talent IDs
      const azureTalentIds = azureResult.recordset.map(t => t.talentId.toLowerCase());
      const matchedTalentIds = [];
      
      for (const azureTalentId of azureTalentIds) {
        let pgTalentId = talentAzureToPostgresMap.get(azureTalentId);
        if (pgTalentId) {
          console.log(`🔧 [Backend] Found existing talent in PostgreSQL with ID: ${pgTalentId} for Azure ID: ${azureTalentId}`);
          matchedTalentIds.push(pgTalentId);
        } else {
          console.log(`🔧 [Backend] No existing talent found for Azure ID: ${azureTalentId}, attempting to import from Azure...`);
          
          // Try to import the talent from Azure
          try {
            // Query Azure for the talent details
            const talentResult = await azurePool.request()
              .input('talentId', sql.UniqueIdentifier, azureTalentId)
              .query(`
                SELECT 
                  t.Id,
                  t.Name,
                  t.Description,
                  t.ContactName,
                  t.ContactEmailAddress,
                  t.ContactPhoneNumber,
                  t.ContactWebsite,
                  t.Url,
                  t.CreatedOn,
                  t.UpdatedOn,
                  t.ProfileImageCropperValue as cropData,
                  t.FeatureLevel
                FROM Talent t
                WHERE t.Id = @talentId
              `);
            
            if (talentResult.recordset.length === 0) {
              console.log(`🔧 [Backend] No talent found in Azure with ID: ${azureTalentId}`);
              stats.talentsNotFound++;
              continue;
            }
            
            const azureTalent = talentResult.recordset[0];
            console.log(`🔧 [Backend] Found talent in Azure: ${azureTalent.Name}, importing to PostgreSQL...`);
            
            // Fetch genre information from the junction table with multiple fallback strategies
            let genre = [];
            try {
              // Try first with the EntertainmentGenreModelTalentModel table
              try {
                const genreResult = await azurePool.request()
                  .input('talentId', sql.UniqueIdentifier, azureTalentId)
                  .query(`
                    SELECT eg.Name as GenreName
                    FROM EntertainmentGenreModelTalentModel egtml
                    JOIN EntertainmentGenres eg ON egtml.GenresId = eg.Id
                    WHERE egtml.TalentsId = @talentId
                  `);
                  
                if (genreResult.recordset.length > 0) {
                  genre = genreResult.recordset.map(g => g.GenreName);
                  console.log(`🔧 [Backend] Found ${genre.length} genres for talent: ${genre.join(', ')}`);
                } else {
                  console.log(`🔧 [Backend] No genres found in EntertainmentGenreModelTalentModel table`);
                }
              } catch (entGenreError) {
                // If first attempt fails, try with Talent.GenreId if it exists
                console.log(`🔧 [Backend] Failed to query EntertainmentGenreModelTalentModel, trying fallback...`);
                
                try {
                  // Check if Talent has GenreId column
                  const hasGenreIdResult = await azurePool.request()
                    .query(`
                      SELECT TOP 1 1
                      FROM INFORMATION_SCHEMA.COLUMNS
                      WHERE TABLE_NAME = 'Talent' AND COLUMN_NAME = 'GenreId'
                    `);
                    
                  if (hasGenreIdResult.recordset.length > 0) {
                    // If GenreId exists, try to get genre name from it
                    const directGenreResult = await azurePool.request()
                      .input('talentId', sql.UniqueIdentifier, azureTalentId)
                      .query(`
                        SELECT g.Name as GenreName
                        FROM Talent t
                        JOIN EntertainmentGenres g ON t.GenreId = g.Id
                        WHERE t.Id = @talentId AND t.GenreId IS NOT NULL
                      `);
                      
                    if (directGenreResult.recordset.length > 0) {
                      genre = directGenreResult.recordset.map(g => g.GenreName);
                      console.log(`🔧 [Backend] Found genre from Talent.GenreId: ${genre.join(', ')}`);
                    }
                  }
                } catch (directGenreError) {
                  console.log(`🔧 [Backend] Failed to query direct genre relationship`);
                }
                
                // If still no genres found, check if Category can serve as genre
                if (azureTalent.Category && genre.length === 0) {
                  genre = [azureTalent.Category];
                  console.log(`🔧 [Backend] Using Category as genre fallback: ${azureTalent.Category}`);
                }
              }
            } catch (genreError) {
              console.error(`🔧 [Backend] Failed to fetch genres for talent:`, genreError);
              // Continue without genres if there's an error
            }
            
            // Extract social links from description
            const socialLinks = this.extractSocialLinksFromDescription(azureTalent.Description || '');
            
            // Generate a slug from the name
            let slug = azureTalent.Name
              ? azureTalent.Name.toLowerCase()
                  .replace(/[^a-z0-9]+/g, '-')
                  .replace(/^-+|-+$/g, '')
              : 'talent-' + Date.now();
            
            // Check if a talent with this slug already exists
            const existingTalentBySlug = await queryRunner.query(`
              SELECT id, name, azure_id FROM talent WHERE slug = $1 AND deleted = false
            `, [slug]);
            
            if (existingTalentBySlug.length > 0) {
              const existingTalent = existingTalentBySlug[0];
              console.log(`🔧 [Backend] Found existing talent with slug '${slug}': ${existingTalent.name} (ID: ${existingTalent.id}, Azure ID: ${existingTalent.azure_id})`);
              
              // If the existing talent has the same Azure ID, use it
              if (existingTalent.azure_id && existingTalent.azure_id.toLowerCase() === azureTalentId.toLowerCase()) {
                console.log(`🔧 [Backend] Existing talent matches Azure ID, using existing talent ID: ${existingTalent.id}`);
                pgTalentId = existingTalent.id;
                // Update our map for future lookups
                talentAzureToPostgresMap.set(azureTalentId, pgTalentId);
                matchedTalentIds.push(pgTalentId);
                continue;
              } else {
                // Different Azure ID, generate a unique slug by appending a number
                let counter = 1;
                let uniqueSlug = `${slug}-${counter}`;
                
                while (true) {
                  const slugCheck = await queryRunner.query(`
                    SELECT id FROM talent WHERE slug = $1 AND deleted = false
                  `, [uniqueSlug]);
                  
                  if (slugCheck.length === 0) {
                    slug = uniqueSlug;
                    console.log(`🔧 [Backend] Generated unique slug: ${slug}`);
                    break;
                  }
                  
                  counter++;
                  uniqueSlug = `${slug}-${counter}`;
                }
              }
            }
              
            // Create social media JSON object
            const socialMediaJson = JSON.stringify({
              facebook: socialLinks.facebook || null,
              twitter: socialLinks.twitter || null,
              instagram: socialLinks.instagram || null,
              youtube: socialLinks.youtube || null,
              spotify: socialLinks.spotify || null
            });
            
            // Create new talent in PostgreSQL with correct column names
            // Using quoted column names to preserve case sensitivity
            const result = await queryRunner.query(`
              INSERT INTO talent (
                name, slug, bio, email, "phoneNumber", website, 
                "socialMedia", genre, azure_id, "createdAt", "updatedAt", deleted,
                "cropData", featured, videos
              ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
              )
              RETURNING id
            `, [
              azureTalent.Name,
              slug,
              azureTalent.Description, // Map Description to bio
              azureTalent.ContactEmailAddress,
              azureTalent.ContactPhoneNumber,
              azureTalent.ContactWebsite,
              socialMediaJson, // Combined social media object
              `{${genre.map(g => `"${g}"`).join(',')}}`, // Format as PostgreSQL array syntax
              azureTalentId,
              new Date(),
              new Date(),
              false,
              azureTalent.cropData ? JSON.stringify(azureTalent.cropData) : null,
              false, // featured
              '{}' // empty videos object
            ]);
            
            pgTalentId = result[0].id;
            console.log(`🔧 [Backend] Successfully imported talent with ID: ${pgTalentId}`);
            
            matchedTalentIds.push(pgTalentId);
            
            // Update our map for future lookups
            talentAzureToPostgresMap.set(azureTalentId, pgTalentId);
            
            // Schedule image import in the background if we implement that feature later
            
          } catch (importError) {
            console.error(`🔧 [Backend] Failed to import talent from Azure:`, importError);
            stats.talentsNotFound++;
          }
        }
      }
      
      // Find talents to add (in Azure but not in Postgres)
      const talentsToAdd = matchedTalentIds.filter(id => !currentEventTalentIds.has(id));
      
      // Find talents to remove (in Postgres but not in Azure)
      const talentsToRemove = Array.from(currentEventTalentIds)
        .filter(id => !matchedTalentIds.includes(id));
      
      // Add missing relationships
      if (talentsToAdd.length > 0) {
        for (const talentId of talentsToAdd) {
          await queryRunner.query(`
            INSERT INTO event_talents (event_id, talent_id)
            VALUES ($1, $2)
            ON CONFLICT DO NOTHING
          `, [event.id, talentId]);
          stats.relationshipsAdded++;
        }
      }
      
      // Remove incorrect relationships
      if (talentsToRemove.length > 0) {
        for (const talentId of talentsToRemove) {
          await queryRunner.query(`
            DELETE FROM event_talents 
            WHERE event_id = $1 AND talent_id = $2
          `, [event.id, talentId]);
          stats.relationshipsRemoved++;
        }
      }

      await queryRunner.commitTransaction();
      return { 
        success: true, 
        message: `Fixed event "${event.name}": added ${stats.relationshipsAdded} talent relationships, removed ${stats.relationshipsRemoved} talent relationships, ${stats.talentsNotFound} talents not found`
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Fix all event-talent relationships by calling individual fixes for each event
   * This approach aggregates individual fixes which have proven to work reliably
   */
  @Post('fix-all-aggregated')
  async fixAllEventTalentRelationshipsAggregated(@Query('year') year?: string) {
    console.log(`🔧 [Backend] Starting aggregated batch fix for all event-talent relationships with year filter: ${year || 'none'}`);
    
    try {
      // Get all events that have missing or mismatched talent relationships with the same year filter as frontend
      const missingEvents = await this.getEventsWithMissingTalents(year);
      const mismatchedEvents = await this.getEventsWithMismatchedTalents(year);
      
      // Combine and deduplicate events
      const allProblematicEvents = new Map();
      
      missingEvents.forEach(event => {
        allProblematicEvents.set(event.id, event);
      });
      
      mismatchedEvents.forEach(event => {
        allProblematicEvents.set(event.id, event);
      });
      
      const eventsToFix = Array.from(allProblematicEvents.values());
      
      console.log(`🔧 [Backend] Found ${eventsToFix.length} events that need fixing (year filter: ${year || 'none'})`);
      
      if (eventsToFix.length === 0) {
        return {
          success: true,
          message: 'No events found that need fixing',
          stats: {
            totalProcessed: 0,
            successfulFixes: 0,
            failedFixes: 0,
            relationshipsAdded: 0,
            relationshipsRemoved: 0,
            talentsNotFound: 0
          }
        };
      }
      
      // Track overall statistics
      const overallStats = {
        totalProcessed: 0,
        successfulFixes: 0,
        failedFixes: 0,
        relationshipsAdded: 0,
        relationshipsRemoved: 0,
        talentsNotFound: 0
      };
      
      // Process each event individually
      for (const event of eventsToFix) {
        try {
          console.log(`🔧 [Backend] Processing event ${overallStats.totalProcessed + 1}/${eventsToFix.length}: ${event.name} (ID: ${event.id})`);
          
          // Call the individual fix function
          const result = await this.fixSingleEventTalentRelationship(event.id.toString());
          
          if (result.success) {
            overallStats.successfulFixes++;
            
            // Parse the message to extract statistics
            const message = result.message;
            const addedMatch = message.match(/added (\d+) talent relationships/);
            const removedMatch = message.match(/removed (\d+) talent relationships/);
            const notFoundMatch = message.match(/(\d+) talents not found/);
            
            if (addedMatch) {
              overallStats.relationshipsAdded += parseInt(addedMatch[1]);
            }
            if (removedMatch) {
              overallStats.relationshipsRemoved += parseInt(removedMatch[1]);
            }
            if (notFoundMatch) {
              overallStats.talentsNotFound += parseInt(notFoundMatch[1]);
            }
            
            console.log(`🔧 [Backend] Successfully fixed event: ${event.name}`);
          } else {
            overallStats.failedFixes++;
            console.error(`🔧 [Backend] Failed to fix event: ${event.name}`);
          }
        } catch (error) {
          overallStats.failedFixes++;
          console.error(`🔧 [Backend] Error fixing event ${event.name}:`, error);
        }
        
        overallStats.totalProcessed++;
      }
      
      const yearInfo = year ? ` (${year}+ events only)` : ' (all events)';
      const successMessage = `Aggregated batch fix completed${yearInfo}: processed ${overallStats.totalProcessed} events, ${overallStats.successfulFixes} successful, ${overallStats.failedFixes} failed, added ${overallStats.relationshipsAdded} relationships, removed ${overallStats.relationshipsRemoved} relationships, ${overallStats.talentsNotFound} talents not found`;
      
      console.log(`🔧 [Backend] ${successMessage}`);
      
      return {
        success: true,
        message: successMessage,
        stats: overallStats
      };
      
    } catch (error) {
      console.error('🔧 [Backend] Error in aggregated batch fix:', error);
      throw error;
    }
  }

  @Post('fix-from-csv')
  @ApiOperation({ summary: 'Fix event-talent relationships using CSV data with Azure MSSQL validation' })
  @ApiResponse({ status: 200, description: 'Event-talent relationships fixed successfully' })
  async fixEventTalentRelationshipsFromCsv(): Promise<any> {
    console.log('🔧 [Backend] Starting CSV-based event-talent relationship fix with Azure validation...');
    
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    let transactionStarted = false;
    let azurePool = null;
    
    try {
      // 1. Connect to Azure MSSQL for validation
      const azureConfig = {
        server: 'mssql.drv1.umbhost.net',
        database: 'TLM',
        user: 'Reader',
        password: 'TLM1234!',
        options: {
          encrypt: false
        }
      };
      
      console.log('🔗 [Backend] Connecting to Azure MSSQL for validation...');
      azurePool = await sql.connect(azureConfig);
      console.log('✅ [Backend] Connected to Azure MSSQL successfully');
      
      // 2. Read and parse CSV file correctly
      const csvPath = path.join(process.cwd(), 'data/imports/talentjoins.csv');
      console.log('📁 [Backend] Reading CSV from:', csvPath);
      
      const csvContent = fs.readFileSync(csvPath, 'utf-8');
      const lines = csvContent.trim().split('\n');
      const header = lines[0];
      const dataLines = lines.slice(1);
      
      console.log(`📊 [Backend] CSV contains ${dataLines.length} relationships`);
      
      // 3. Parse CSV relationships as GUIDs (NOT integers!)
      const csvRelationships = dataLines.map((line, index) => {
        const [eventGuid, talentGuid] = line.split(',').map(id => id.trim());
        
        // Validate GUID format
        const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (!guidRegex.test(eventGuid) || !guidRegex.test(talentGuid)) {
          console.warn(`⚠️ [Backend] Invalid GUID format at line ${index + 2}: Event=${eventGuid}, Talent=${talentGuid}`);
          return null;
        }
        
        return {
          eventGuid: eventGuid.toLowerCase(),
          talentGuid: talentGuid.toLowerCase()
        };
      }).filter(rel => rel !== null);
      
      console.log(`✅ [Backend] Parsed ${csvRelationships.length} valid GUID relationships`);
      
      // 4. Validate relationships against Azure MSSQL source
      console.log('🔍 [Backend] Validating relationships against Azure MSSQL source...');
      const validatedRelationships = [];
      let invalidEventCount = 0;
      let invalidTalentCount = 0;
      let validRelationshipCount = 0;

      // PERFORMANCE OPTIMIZATION: Batch validation instead of individual queries
      // Get all unique event and talent IDs from CSV
      const uniqueEventIds = [...new Set(csvRelationships.map(r => r.eventGuid))];
      const uniqueTalentIds = [...new Set(csvRelationships.map(r => r.talentGuid))];

      console.log(`📊 [Backend] Validating ${uniqueEventIds.length} unique events and ${uniqueTalentIds.length} unique talents...`);

      // MSSQL has a 2100 parameter limit, so process in chunks
      const validationBatchSize = 1000; // Safe batch size for MSSQL parameters

      // Batch query 1: Get all valid events from Azure in chunks
      const validEventIds = new Set();
      for (let i = 0; i < uniqueEventIds.length; i += validationBatchSize) {
        const batchEventIds = uniqueEventIds.slice(i, i + validationBatchSize);
        const eventIdParams = batchEventIds.map((_, index) => `@eventId${index}`).join(',');
        const eventRequest = azurePool.request();
        batchEventIds.forEach((eventId, index) => {
          eventRequest.input(`eventId${index}`, sql.UniqueIdentifier, eventId);
        });

        const batchEventsResult = await eventRequest.query(`
          SELECT Id FROM PerformanceEvents WHERE Id IN (${eventIdParams})
        `);

        batchEventsResult.recordset.forEach(row => {
          validEventIds.add(row.Id.toLowerCase());
        });

        console.log(`📊 [Backend] Event validation batch ${Math.floor(i / validationBatchSize) + 1}: ${batchEventsResult.recordset.length} valid events`);
      }

      // Batch query 2: Get all valid talents from Azure in chunks
      const validTalentIds = new Set();
      for (let i = 0; i < uniqueTalentIds.length; i += validationBatchSize) {
        const batchTalentIds = uniqueTalentIds.slice(i, i + validationBatchSize);
        const talentIdParams = batchTalentIds.map((_, index) => `@talentId${index}`).join(',');
        const talentRequest = azurePool.request();
        batchTalentIds.forEach((talentId, index) => {
          talentRequest.input(`talentId${index}`, sql.UniqueIdentifier, talentId);
        });

        const batchTalentsResult = await talentRequest.query(`
          SELECT Id FROM Talent WHERE Id IN (${talentIdParams})
        `);

        batchTalentsResult.recordset.forEach(row => {
          validTalentIds.add(row.Id.toLowerCase());
        });

        console.log(`📊 [Backend] Talent validation batch ${Math.floor(i / validationBatchSize) + 1}: ${batchTalentsResult.recordset.length} valid talents`);
      }

      // Batch query 3: Get all valid relationships from Azure junction table in chunks
      // Each relationship needs 2 parameters, so max relationships per batch = 1000
      const relationshipBatchSize = 1000;
      const validRelationshipKeys = new Set();

      for (let i = 0; i < csvRelationships.length; i += relationshipBatchSize) {
        const batchRelationships = csvRelationships.slice(i, i + relationshipBatchSize);

        const relationshipConditions = batchRelationships.map((_, index) =>
          `(PerformanceEventsId = @eventId${index} AND TalentListId = @talentId${index})`
        ).join(' OR ');

        const relationshipRequest = azurePool.request();
        batchRelationships.forEach((relationship, index) => {
          relationshipRequest.input(`eventId${index}`, sql.UniqueIdentifier, relationship.eventGuid);
          relationshipRequest.input(`talentId${index}`, sql.UniqueIdentifier, relationship.talentGuid);
        });

        const batchRelationshipsResult = await relationshipRequest.query(`
          SELECT PerformanceEventsId, TalentListId
          FROM PerformanceEventModelTalentModel
          WHERE ${relationshipConditions}
        `);

        batchRelationshipsResult.recordset.forEach(row => {
          validRelationshipKeys.add(`${row.PerformanceEventsId.toLowerCase()}-${row.TalentListId.toLowerCase()}`);
        });

        console.log(`📊 [Backend] Relationship validation batch ${Math.floor(i / relationshipBatchSize) + 1}: ${batchRelationshipsResult.recordset.length} valid relationships`);
      }

      console.log(`✅ [Backend] Batch validation complete - found ${validEventIds.size} valid events, ${validTalentIds.size} valid talents, ${validRelationshipKeys.size} valid relationships`);

      // Now validate each relationship using the cached results
      for (const relationship of csvRelationships) {
        const eventId = relationship.eventGuid.toLowerCase();
        const talentId = relationship.talentGuid.toLowerCase();
        const relationshipKey = `${eventId}-${talentId}`;

        if (!validEventIds.has(eventId)) {
          invalidEventCount++;
          continue;
        }

        if (!validTalentIds.has(talentId)) {
          invalidTalentCount++;
          continue;
        }

        if (!validRelationshipKeys.has(relationshipKey)) {
          continue;
        }

        validatedRelationships.push(relationship);
        validRelationshipCount++;
      }
      
      console.log(`✅ [Backend] Azure validation complete:`);
      console.log(`   - Valid relationships: ${validRelationshipCount}`);
      console.log(`   - Invalid events: ${invalidEventCount}`);
      console.log(`   - Invalid talents: ${invalidTalentCount}`);
      
      if (validatedRelationships.length === 0) {
        return {
          success: true,
          message: 'No valid relationships found after Azure validation',
          stats: {
            totalCsvRelationships: csvRelationships.length,
            validatedRelationships: 0,
            invalidEventCount,
            invalidTalentCount,
            relationshipsInserted: 0
          }
        };
      }
      
      // 5. Map Azure GUIDs to PostgreSQL IDs using azure_id fields
      console.log('🗺️ [Backend] Mapping Azure GUIDs to PostgreSQL IDs...');
      
      // Get all events with Azure IDs (stored as strings, not integers!)
      const eventsWithAzureIds = await queryRunner.manager.find(Event, {
        where: { azure_id: Not(IsNull()) },
        select: ['id', 'azure_id']
      });
      
      // Get all talents with Azure IDs (stored as strings, not integers!)
      const talentsWithAzureIds = await queryRunner.manager.find(Talent, {
        where: { azure_id: Not(IsNull()) },
        select: ['id', 'azure_id']
      });
      
      // Create maps for quick lookup using string GUIDs
      const eventAzureToPostgresMap = new Map<string, string>();
      eventsWithAzureIds.forEach(event => {
        if (event.azure_id) {
          eventAzureToPostgresMap.set(event.azure_id.toLowerCase(), event.id);
        }
      });
      
      const talentAzureToPostgresMap = new Map<string, string>();
      talentsWithAzureIds.forEach(talent => {
        if (talent.azure_id) {
          talentAzureToPostgresMap.set(talent.azure_id.toLowerCase(), talent.id.toString());
        }
      });
      
      console.log(`🗺️ [Backend] Event mapping: ${eventAzureToPostgresMap.size} events`);
      console.log(`🗺️ [Backend] Talent mapping: ${talentAzureToPostgresMap.size} talents`);
      
      // 6. Get existing relationships to avoid duplicates
      const existingRelationships = await queryRunner.manager.find(EventTalent, {
        select: ['eventId', 'talentId']
      });
      
      const existingRelationshipSet = new Set<string>();
      existingRelationships.forEach(rel => {
        existingRelationshipSet.add(`${rel.eventId}-${rel.talentId}`);
      });
      
      console.log(`📋 [Backend] Found ${existingRelationships.length} existing relationships`);
      
      // 7. Process validated relationships and identify what to insert
      const relationshipsToInsert: { eventId: string; talentId: string }[] = [];
      let skippedMissingEvent = 0;
      let skippedMissingTalent = 0;
      let skippedExisting = 0;
      
      for (const validatedRel of validatedRelationships) {
        const postgresEventId = eventAzureToPostgresMap.get(validatedRel.eventGuid);
        const postgresTalentId = talentAzureToPostgresMap.get(validatedRel.talentGuid);
        
        if (!postgresEventId) {
          skippedMissingEvent++;
          console.warn(`⚠️ [Backend] Event not found in PostgreSQL: ${validatedRel.eventGuid}`);
          continue;
        }
        
        if (!postgresTalentId) {
          skippedMissingTalent++;
          console.warn(`⚠️ [Backend] Talent not found in PostgreSQL: ${validatedRel.talentGuid}`);
          continue;
        }
        
        const relationshipKey = `${postgresEventId}-${postgresTalentId}`;
        if (existingRelationshipSet.has(relationshipKey)) {
          skippedExisting++;
          continue;
        }
        
        relationshipsToInsert.push({
          eventId: postgresEventId,
          talentId: postgresTalentId
        });
      }
      
      console.log(`📊 [Backend] Relationships to insert: ${relationshipsToInsert.length}`);
      console.log(`⏭️ [Backend] Skipped - missing event in PostgreSQL: ${skippedMissingEvent}`);
      console.log(`⏭️ [Backend] Skipped - missing talent in PostgreSQL: ${skippedMissingTalent}`);
      console.log(`⏭️ [Backend] Skipped - already exists: ${skippedExisting}`);
      
      if (relationshipsToInsert.length === 0) {
        return {
          success: true,
          message: 'No new relationships to insert after validation and mapping',
          stats: {
            totalCsvRelationships: csvRelationships.length,
            validatedRelationships: validatedRelationships.length,
            relationshipsInserted: 0,
            skippedMissingEvent,
            skippedMissingTalent,
            skippedExisting,
            invalidEventCount,
            invalidTalentCount
          }
        };
      }
      
      // 8. Start transaction and insert relationships
      await queryRunner.startTransaction();
      transactionStarted = true;
      
      // Insert relationships in batches with proper duplicate handling
      const batchSize = 1000;
      let totalInserted = 0;
      
      for (let i = 0; i < relationshipsToInsert.length; i += batchSize) {
        const batch = relationshipsToInsert.slice(i, i + batchSize);
        
        // Use raw query with ON CONFLICT for better duplicate handling
        const values = batch.map(() => '(?, ?)').join(', ');
        const params = batch.flatMap(rel => [rel.eventId, rel.talentId]);
        
        const result = await queryRunner.query(`
          INSERT INTO event_talents (event_id, talent_id)
          VALUES ${values}
          ON CONFLICT (event_id, talent_id) DO NOTHING
          RETURNING event_id, talent_id
        `, params);
        
        const actualInserted = result.length;
        totalInserted += actualInserted;
        
        console.log(`💾 [Backend] Processed batch ${Math.floor(i / batchSize) + 1}: ${actualInserted}/${batch.length} relationships inserted (Total: ${totalInserted})`);
      }
      
      await queryRunner.commitTransaction();
      
      console.log(`✅ [Backend] CSV-based fix completed successfully with Azure validation`);
      console.log(`📊 [Backend] Final stats: ${totalInserted} relationships inserted`);
      
      return {
        success: true,
        message: `Successfully processed CSV data with Azure validation and inserted ${totalInserted} new event-talent relationships`,
        stats: {
          totalCsvRelationships: csvRelationships.length,
          validatedRelationships: validatedRelationships.length,
          relationshipsInserted: totalInserted,
          skippedMissingEvent,
          skippedMissingTalent,
          skippedExisting,
          invalidEventCount,
          invalidTalentCount
        }
      };
      
    } catch (error) {
      console.error('🔧 [Backend] Error in fixEventTalentRelationshipsFromCsv:', error);
      if (transactionStarted) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      // Clean up connections
      if (azurePool) {
        await azurePool.close();
        console.log('🔗 [Backend] Azure MSSQL connection closed');
      }
      await queryRunner.release();
    }
  }

  /**
   * Fix all event-talent relationships by synchronizing from Azure to PostgreSQL
   * @deprecated Use fixAllEventTalentRelationshipsAggregated instead
   */
  @Post('fix-all')
  async fixAllEventTalentRelationships() {
    let azurePool = null;
    
    // Use a query runner for PostgreSQL transactions
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    
    try {
      console.log('🔧 [Backend] Starting bulk fix for all event-talent relationships');
      
      // Connect to Azure SQL with improved error handling
      try {
        azurePool = await this.getAzurePool();
        console.log('🔧 [Backend] Successfully connected to Azure SQL');
      } catch (azureError) {
        console.error('🔧 [Backend] Failed to connect to Azure SQL:', azureError);
        throw new Error('Azure SQL connection failed');
      }

      // Create stats object to track progress
      const stats = {
        totalProcessed: 0,
        relationshipsAdded: 0,
        relationshipsRemoved: 0,
        talentsNotFound: 0,
        eventsNotFound: 0,
        errors: 0
      };
      
      // Get all events from PostgreSQL with Azure IDs
      const pgEvents = await queryRunner.query(`
        SELECT id, azure_id, name
        FROM event
        WHERE deleted = false AND azure_id IS NOT NULL
        LIMIT 500
      `);
      
      console.log(`🔧 [Backend] Found ${pgEvents.length} events in PostgreSQL with Azure IDs`);
      
      if (pgEvents.length === 0) {
        console.log('🔧 [Backend] No events found with Azure IDs, nothing to process');
        await queryRunner.commitTransaction();
        return {
          success: true,
          message: 'No events found with Azure IDs to process'
        };
      }

      // Get all talents from PostgreSQL with their Azure IDs
      const pgTalents = await queryRunner.query(`
        SELECT id, azure_id, name
        FROM talent
        WHERE deleted = false AND azure_id IS NOT NULL
      `);
      
      console.log(`🔧 [Backend] Found ${pgTalents.length} talents in PostgreSQL with Azure IDs`);
      
      // Create a map of Azure ID to PostgreSQL ID for quick lookups
      const talentAzureToPostgresMap = new Map();
      pgTalents.forEach(talent => {
        if (talent.azure_id) {
          talentAzureToPostgresMap.set(talent.azure_id.toLowerCase(), talent.id);
        }
      });
      
      // Get all existing event-talent relationships
      const currentRelationships = await queryRunner.query(`
        SELECT event_id, talent_id FROM event_talents
      `);
      
      // Create a map to efficiently track existing relationships
      const relationshipMap = new Map();
      currentRelationships.forEach(rel => {
        if (!relationshipMap.has(rel.event_id)) {
          relationshipMap.set(rel.event_id, new Set());
        }
        relationshipMap.get(rel.event_id).add(rel.talent_id);
      });

      console.log(`🔧 [Backend] Starting to process ${pgEvents.length} events`);
      
      // Process events in batches
      for (let i = 0; i < pgEvents.length; i++) {
        const event = pgEvents[i];
        
        try {
          // Log progress every 50 events
          if (i % 50 === 0 || i === pgEvents.length - 1) {
            console.log(`🔧 [Backend] Processing event ${i + 1}/${pgEvents.length}: ${event.name}`);
          }
          
          // Skip events without an Azure ID
          if (!event.azure_id) {
            stats.eventsNotFound++;
            continue;
          }
          
          // Get all talents for this event from Azure
          let azureResult;
          try {
            azureResult = await azurePool.request()
              .input('eventId', sql.UniqueIdentifier, event.azure_id)
              .query(`
                SELECT t.Id as talentId
                FROM PerformanceEventModelTalentModel pemtm
                JOIN Talent t ON pemtm.TalentListId = t.Id
                WHERE pemtm.PerformanceEventsId = @eventId
              `);
          } catch (azureQueryError) {
            console.error(`🔧 [Backend] Failed to query Azure for event ${event.name}:`, azureQueryError);
            stats.errors++;
            continue;
          }
          
          // Get Azure talent IDs and map them to PostgreSQL talent IDs
          const azureTalentIds = azureResult.recordset.map(t => t.talentId.toLowerCase());
          const matchedTalentIds = [];
          
          for (const azureTalentId of azureTalentIds) {
            const pgTalentId = talentAzureToPostgresMap.get(azureTalentId);
            if (pgTalentId) {
              matchedTalentIds.push(pgTalentId);
            } else {
              stats.talentsNotFound++;
            }
          }
          
          // Get current relationships for this event
          const currentEventTalentIds = relationshipMap.get(event.id) || new Set();
          
          // Find talents to add (in Azure but not in Postgres)
          const talentsToAdd = matchedTalentIds.filter(id => !currentEventTalentIds.has(id));
          
          // Find talents to remove (in Postgres but not in Azure)
          const talentsToRemove = Array.from(currentEventTalentIds)
            .filter(id => !matchedTalentIds.includes(id));
          
          // Add missing relationships
          if (talentsToAdd.length > 0) {
            for (const talentId of talentsToAdd) {
              try {
                await queryRunner.query(`
                  INSERT INTO event_talents (event_id, talent_id)
                  VALUES ($1, $2)
                  ON CONFLICT DO NOTHING
                `, [event.id, talentId]);
                stats.relationshipsAdded++;
              } catch (error) {
                console.error(`Error adding talent relationship: ${error instanceof Error ? error.message : 'Unknown error'}`);
                stats.errors++;
              }
            }
          }
          
          // Remove incorrect relationships
          if (talentsToRemove.length > 0) {
            for (const talentId of talentsToRemove) {
              try {
                await queryRunner.query(`
                  DELETE FROM event_talents 
                  WHERE event_id = $1 AND talent_id = $2
                `, [event.id, talentId]);
                stats.relationshipsRemoved++;
              } catch (error) {
                console.error(`Error removing talent relationship: ${error instanceof Error ? error.message : 'Unknown error'}`);
                stats.errors++;
              }
            }
          }
          
          stats.totalProcessed++;
          
        } catch (error) {
          console.error(`🔧 [Backend] Error processing event ${event.name} (${event.id}): ${error instanceof Error ? error.message : 'Unknown error'}`);
          stats.errors++;
        }
      }

      console.log(`🔧 [Backend] Bulk fix completed successfully`);
      console.log(`🔧 [Backend] Final stats: processed ${stats.totalProcessed} events, added ${stats.relationshipsAdded} relationships, removed ${stats.relationshipsRemoved} relationships, ${stats.talentsNotFound} talents not found, ${stats.errors} errors`);

      await queryRunner.commitTransaction();
      return { 
        success: true, 
        message: `Processed ${stats.totalProcessed} events, added ${stats.relationshipsAdded} talent relationships, removed ${stats.relationshipsRemoved} talent relationships, ${stats.talentsNotFound} talents not found, ${stats.errors} errors`
      };
    } catch (error) {
      console.error('🔧 [Backend] Error in fixAllEventTalentRelationships:', error);
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}
